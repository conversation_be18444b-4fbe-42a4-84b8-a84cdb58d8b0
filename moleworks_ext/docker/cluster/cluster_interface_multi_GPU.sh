#!/usr/bin/env bash

#==
# Simplified Multi-GPU Training Interface for SLURM Cluster
#==

# Exits if error occurs
set -e

# Set tab-spaces
tabs 4

# get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"

# Load custom environment paths if provided, else use defaults
ENV_CLUSTER_PATH=${ENV_CLUSTER_PATH:-"$SCRIPT_DIR/.env.cluster"}
# Codebase to sync
CODEBASE_PATH=${CODEBASE_PATH:-"$SCRIPT_DIR/../.."}

#==
# GPU Configuration Table - Based on actual Euler cluster specifications
#==
declare -A GPU_CONFIGS
# Format: "gpu_type:gpus_per_node:cpus_per_node:memory_per_node_gb"
GPU_CONFIGS["rtx_2080_ti"]="8:36:384"
GPU_CONFIGS["rtx_2080_ti_large"]="8:128:512"
GPU_CONFIGS["rtx_3090"]="8:128:512"
GPU_CONFIGS["rtx_4090"]="8:128:512"
GPU_CONFIGS["titan_rtx"]="8:128:512"
GPU_CONFIGS["quadro_rtx_6000"]="8:128:512"
GPU_CONFIGS["v100"]="8:48:768"
GPU_CONFIGS["v100_small"]="8:40:512"
GPU_CONFIGS["a100-pcie-40gb"]="8:48:768"
GPU_CONFIGS["a100_80gb"]="10:48:1024"

# Default configuration for multi-GPU training
DEFAULT_NUM_GPUS=4
DEFAULT_GPU_TYPE="rtx_4090"
DEFAULT_TIME="23:00:00"

#==
# Helper Functions
#==

# Function to get GPU configuration
get_gpu_config() {
    local gpu_type="$1"
    if [[ -n "${GPU_CONFIGS[$gpu_type]}" ]]; then
        echo "${GPU_CONFIGS[$gpu_type]}"
    else
        echo ""
    fi
}

# Function to automatically select optimal GPU configuration
auto_select_gpu_config() {
    local num_envs="$1"
    local num_gpus="$2"
    
    # Calculate memory requirements (rough estimate)
    local mem_per_env=50  # MB per environment (conservative estimate)
    local total_mem_needed=$((num_envs * mem_per_env / 1024))  # Convert to GB
    local mem_per_gpu=$((total_mem_needed / num_gpus))
    
    # Select GPU type based on memory requirements and availability
    if [ "$mem_per_gpu" -gt 60 ]; then
        echo "a100_80gb"  # 80GB VRAM
    elif [ "$mem_per_gpu" -gt 30 ]; then
        echo "a100-pcie-40gb"  # 40GB VRAM
    elif [ "$mem_per_gpu" -gt 20 ]; then
        echo "rtx_4090"  # 24GB VRAM
    else
        echo "rtx_3090"  # 24GB VRAM
    fi
}

# Function to calculate optimal resources
calculate_resources() {
    local gpu_type="$1"
    local num_gpus="$2"
    
    local gpu_config=$(get_gpu_config "$gpu_type")
    if [[ -z "$gpu_config" ]]; then
        echo "Error: Unknown GPU type '$gpu_type'" >&2
        return 1
    fi
    
    IFS=':' read -r gpus_per_node cpus_per_node mem_per_node <<< "$gpu_config"
    
    # Validate that requested GPUs don't exceed node capacity
    if [ "$num_gpus" -gt "$gpus_per_node" ]; then
        echo "Error: Requested $num_gpus GPUs exceeds the maximum of $gpus_per_node GPUs per node for $gpu_type" >&2
        return 1
    fi
    
    # Calculate resources based on actual node configuration
    if [ "$num_gpus" -eq "$gpus_per_node" ]; then
        # Using full node
        TOTAL_CPUS=$cpus_per_node
        TOTAL_MEM=$mem_per_node
    else
        # Using partial node - scale resources proportionally
        local cpus_per_gpu=$((cpus_per_node / gpus_per_node))
        local mem_per_gpu=$((mem_per_node / gpus_per_node))
        TOTAL_CPUS=$((num_gpus * cpus_per_gpu))
        TOTAL_MEM=$((num_gpus * mem_per_gpu))
    fi
    
    export NUM_GPUS="$num_gpus"
    export GPU_TYPE="$gpu_type"
    export TOTAL_CPUS
    export TOTAL_MEM
}

help() {
    echo -e "\nusage: $(basename "$0") <command> [<profile>] [<job_args>...] -- Simplified Multi-GPU Training Interface for SLURM Cluster."
    echo -e "\ncommands:"
    echo -e "  job [<profile>] [<job_args>]  Submit a multi-GPU training job to the cluster."
    echo -e "\nwhere:"
    echo -e "  <profile>  is the optional container profile specification. Defaults to 'moleworks_ext'."
    echo -e "  <job_args> are arguments for the training job."
    echo -e "\nRequired job arguments:"
    echo -e "  --task TASK                   Training task name (e.g., Isaac-m545-digging)"
    echo -e "  --num_envs NUM_ENVS          Number of environments"
    echo -e "\nOptional job arguments:"
    echo -e "  --num_gpus NUM_GPUS          Number of GPUs to use (default: $DEFAULT_NUM_GPUS)"
    echo -e "  --gpu_type GPU_TYPE          GPU type to request (default: auto-select)"
    echo -e "  --max_iterations ITERS       Maximum training iterations (default: 1000)"
    echo -e "  --time TIME                  Job time limit (default: $DEFAULT_TIME)"
    echo -e "\nExamples:"
    echo -e "  # Basic multi-GPU training with auto-selected GPU type"
    echo -e "  $(basename "$0") job moleworks_ext --task Isaac-m545-digging --num_envs 64000"
    echo -e ""
    echo -e "  # Specify number of GPUs and GPU type"
    echo -e "  $(basename "$0") job moleworks_ext --task Isaac-m545-digging --num_envs 128000 --num_gpus 8 --gpu_type a100_80gb"
    echo -e ""
    echo -e "Available GPU types:"
    for gpu_type in "${!GPU_CONFIGS[@]}"; do
        IFS=':' read -r gpus_per_node cpus_per_node mem_per_node <<< "${GPU_CONFIGS[$gpu_type]}"
        printf "  %-20s - %2d GPUs/node, %3d CPUs/node, %4d GB/node\n" \
            "$gpu_type" "$gpus_per_node" "$cpus_per_node" "$mem_per_node"
    done | sort
    echo -e "\n" >&2
}

#==
# Main Script Logic
#==

# Check for command
if [ $# -lt 1 ]; then
    echo "Error: Command is required." >&2
    help
    exit 1
fi

command=$1
shift

# Handle help command
if [ "$command" = "-h" ] || [ "$command" = "--help" ]; then
    help
    exit 0
fi

# Only support job command for simplicity
if [ "$command" != "job" ]; then
    echo "Error: Only 'job' command is supported." >&2
    help
    exit 1
fi

# Set default profile
profile="moleworks_ext"

# Check if first argument is a profile
if [ $# -ge 1 ] && [ -f "$CODEBASE_PATH/docker/.env.$1" ]; then
    profile=$1
    shift
fi

# Parse job arguments
TASK=""
NUM_ENVS=""
NUM_GPUS=$DEFAULT_NUM_GPUS
GPU_TYPE=""
MAX_ITERATIONS=1000
TIME=$DEFAULT_TIME

while [[ $# -gt 0 ]]; do
    case $1 in
        --task)
            TASK="$2"
            shift 2
            ;;
        --num_envs)
            NUM_ENVS="$2"
            shift 2
            ;;
        --num_gpus)
            NUM_GPUS="$2"
            shift 2
            ;;
        --gpu_type)
            GPU_TYPE="$2"
            shift 2
            ;;
        --max_iterations)
            MAX_ITERATIONS="$2"
            shift 2
            ;;
        --time)
            TIME="$2"
            shift 2
            ;;
        -h|--help)
            help
            exit 0
            ;;
        *)
            echo "Unknown argument: $1"
            help
            exit 1
            ;;
    esac
done

# Validate required arguments
if [[ -z "$TASK" ]]; then
    echo "Error: --task is required" >&2
    help
    exit 1
fi

if [[ -z "$NUM_ENVS" ]]; then
    echo "Error: --num_envs is required" >&2
    help
    exit 1
fi

# Validate numeric arguments
if ! [[ "$NUM_GPUS" =~ ^[0-9]+$ ]] || [ "$NUM_GPUS" -lt 1 ]; then
    echo "Error: --num_gpus must be a positive integer" >&2
    exit 1
fi

if ! [[ "$NUM_ENVS" =~ ^[0-9]+$ ]] || [ "$NUM_ENVS" -lt 1 ]; then
    echo "Error: --num_envs must be a positive integer" >&2
    exit 1
fi

if ! [[ "$MAX_ITERATIONS" =~ ^[0-9]+$ ]] || [ "$MAX_ITERATIONS" -lt 1 ]; then
    echo "Error: --max_iterations must be a positive integer" >&2
    exit 1
fi
